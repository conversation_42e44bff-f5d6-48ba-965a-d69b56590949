import { PrismaClient, PostStatus, TestType } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 开始播种数据库... / Starting database seeding...')

  // 创建示例用户
  // Create sample users
  const user1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'admin',
      locale: 'zh-CN',
      theme: 'light',
    },
  })

  const user2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'testuser',
      locale: 'en',
      theme: 'dark',
    },
  })

  console.log('✅ 用户创建完成 / Users created')

  // 创建示例博客文章
  // Create sample blog posts
  const blogPosts = [
    {
      title: '塔罗牌入门指南：解锁神秘的占卜艺术',
      slug: 'tarot-beginner-guide',
      content: `
        <h2>什么是塔罗牌？</h2>
        <p>塔罗牌是一套由78张牌组成的占卜工具，起源于15世纪的欧洲。每张牌都有独特的象征意义和解读方式。</p>
        
        <h2>塔罗牌的组成</h2>
        <p>塔罗牌分为两个主要部分：</p>
        <ul>
          <li><strong>大阿卡纳（Major Arcana）</strong>：22张牌，代表人生的重大主题和精神旅程</li>
          <li><strong>小阿卡纳（Minor Arcana）</strong>：56张牌，分为四个花色，代表日常生活的各个方面</li>
        </ul>
        
        <h2>如何开始学习塔罗？</h2>
        <p>1. 选择一副适合初学者的塔罗牌<br>
        2. 学习每张牌的基本含义<br>
        3. 练习简单的牌阵<br>
        4. 记录你的解读和感悟</p>
      `,
      excerpt: '探索塔罗牌的神秘世界，从基础知识到实践技巧，为初学者提供全面的入门指导。',
      locale: 'zh-CN',
      category: 'tarot',
      tags: ['塔罗牌', '占卜', '入门指南', '神秘学'],
      status: PostStatus.PUBLISHED,
      publishedAt: new Date(),
      readingTime: 5,
      seoTitle: '塔罗牌入门指南 - 初学者完整教程',
      seoDescription: '完整的塔罗牌入门指南，包含基础知识、牌意解读、实践技巧。适合塔罗初学者的专业教程。',
      keywords: ['塔罗牌', '塔罗入门', '占卜教程', '塔罗学习'],
      metadata: {
        aiGenerated: false,
        featured: true,
        difficulty: 'beginner'
      }
    },
    {
      title: 'Understanding Your Zodiac Sign: A Complete Guide',
      slug: 'zodiac-sign-complete-guide',
      content: `
        <h2>What Are Zodiac Signs?</h2>
        <p>Zodiac signs are twelve astrological divisions based on the position of the sun at the time of your birth. Each sign has unique characteristics and traits.</p>
        
        <h2>The Twelve Zodiac Signs</h2>
        <p>The zodiac is divided into four elements:</p>
        <ul>
          <li><strong>Fire Signs</strong>: Aries, Leo, Sagittarius</li>
          <li><strong>Earth Signs</strong>: Taurus, Virgo, Capricorn</li>
          <li><strong>Air Signs</strong>: Gemini, Libra, Aquarius</li>
          <li><strong>Water Signs</strong>: Cancer, Scorpio, Pisces</li>
        </ul>
        
        <h2>How to Use Astrology</h2>
        <p>Astrology can help you understand your personality, relationships, and life patterns. Use it as a tool for self-reflection and personal growth.</p>
      `,
      excerpt: 'Discover the secrets of your zodiac sign and learn how astrology can guide your personal growth and relationships.',
      locale: 'en',
      category: 'astrology',
      tags: ['zodiac', 'astrology', 'personality', 'horoscope'],
      status: PostStatus.PUBLISHED,
      publishedAt: new Date(),
      readingTime: 7,
      seoTitle: 'Complete Zodiac Signs Guide - Astrology for Beginners',
      seoDescription: 'Learn about all 12 zodiac signs, their characteristics, and how astrology can help you understand yourself better.',
      keywords: ['zodiac signs', 'astrology guide', 'horoscope', 'personality traits'],
      metadata: {
        aiGenerated: false,
        featured: true,
        difficulty: 'beginner'
      }
    },
    {
      title: '数字命理学：解读生命密码的奥秘',
      slug: 'numerology-life-path-guide',
      content: `
        <h2>什么是数字命理学？</h2>
        <p>数字命理学是一门古老的学问，通过分析与个人相关的数字来揭示性格特征、人生目标和命运走向。</p>
        
        <h2>生命路径数字</h2>
        <p>生命路径数字是数字命理学中最重要的数字，通过出生日期计算得出。它揭示了你的人生使命和核心特质。</p>
        
        <h2>如何计算生命路径数字</h2>
        <p>将出生日期的所有数字相加，直到得到单个数字（1-9）或主数字（11、22、33）。</p>
        
        <h2>数字的含义</h2>
        <ul>
          <li><strong>1</strong>：领导者，独立，创新</li>
          <li><strong>2</strong>：合作者，和谐，敏感</li>
          <li><strong>3</strong>：创造者，表达，乐观</li>
        </ul>
      `,
      excerpt: '探索数字命理学的神秘世界，学习如何通过数字解读你的人生密码和命运走向。',
      locale: 'zh-CN',
      category: 'numerology',
      tags: ['数字命理', '生命路径', '命运', '性格分析'],
      status: PostStatus.PUBLISHED,
      publishedAt: new Date(),
      readingTime: 6,
      seoTitle: '数字命理学完整指南 - 解读生命密码',
      seoDescription: '学习数字命理学的基础知识，了解如何计算和解读生命路径数字，发现你的人生使命。',
      keywords: ['数字命理', '生命路径数字', '命理学', '数字占卜'],
      metadata: {
        aiGenerated: false,
        featured: false,
        difficulty: 'intermediate'
      }
    }
  ]

  for (const post of blogPosts) {
    await prisma.blogPost.upsert({
      where: { slug: post.slug },
      update: {},
      create: post,
    })
  }

  console.log('✅ 博客文章创建完成 / Blog posts created')

  // 创建示例测试结果
  // Create sample test results
  const testResults = [
    {
      userId: user1.id,
      testType: TestType.TAROT,
      answers: {
        question1: 'love',
        question2: 'future',
        cards: ['the-fool', 'the-magician', 'the-high-priestess']
      },
      result: {
        interpretation: '你正处于人生的新开始阶段，充满无限可能。',
        cards: [
          { name: '愚者', meaning: '新的开始，冒险精神' },
          { name: '魔术师', meaning: '创造力，意志力' },
          { name: '女祭司', meaning: '直觉，内在智慧' }
        ],
        advice: '相信你的直觉，勇敢地迈出第一步。'
      },
      shareToken: 'tarot-result-123',
      isPublic: true
    },
    {
      userId: user2.id,
      testType: TestType.ASTROLOGY,
      answers: {
        birthDate: '1990-05-15',
        birthTime: '14:30',
        birthPlace: 'Beijing, China'
      },
      result: {
        sunSign: 'Taurus',
        moonSign: 'Cancer',
        risingSign: 'Virgo',
        personality: 'You are practical, emotional, and detail-oriented.',
        strengths: ['Reliable', 'Caring', 'Analytical'],
        challenges: ['Stubborn', 'Overly critical', 'Worry-prone']
      },
      shareToken: 'astro-result-456',
      isPublic: false
    }
  ]

  for (const result of testResults) {
    await prisma.testResult.create({
      data: result,
    })
  }

  console.log('✅ 测试结果创建完成 / Test results created')

  console.log('🎉 数据库播种完成！/ Database seeding completed!')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error('❌ 播种过程中出错 / Error during seeding:', e)
    await prisma.$disconnect()
    process.exit(1)
  })
