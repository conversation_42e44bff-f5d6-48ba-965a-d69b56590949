import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import Image from 'next/image';
import Link from 'next/link';
import { 
  ReadingProgress, 
  TableOfContents, 
  MobileTableOfContents,
  generateTableOfContents 
} from '@/components/blog';
import { BlogPost, Locale } from '@/types';
import {Clock, Eye, Heart, Share2, Tag } from 'lucide-react';

interface BlogPostPageProps {
  params: {
    locale: string;
    category: string;
    slug: string;
  };
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const post = await getPostBySlug(params.category, params.slug, params.locale as Locale);
  
  if (!post) {
    return {
      title: 'Post Not Found',
    };
  }
  
  return {
    title: post.seo.title || post.title,
    description: post.seo.description || post.excerpt,
    keywords: post.seo.keywords,
    authors: [{ name: post.author.name }],
    openGraph: {
      title: post.seo.title || post.title,
      description: post.seo.description || post.excerpt,
      type: 'article',
      locale: params.locale,
      publishedTime: post.publishedAt?.toISOString(),
      modifiedTime: post.updatedAt.toISOString(),
      authors: [post.author.name],
      images: post.coverImage ? [{ url: post.coverImage, alt: post.title }] : [],
    },
    twitter: {
      card: 'summary_large_image',
      title: post.seo.title || post.title,
      description: post.seo.description || post.excerpt,
      images: post.coverImage ? [post.coverImage] : [],
    },
  };
}

// 模拟数据获取函数
async function getPostBySlug(
  category: string, 
  slug: string, 
  locale: Locale
): Promise<BlogPost | null> {
  // 这里应该是实际的数据库查询
  // 暂时返回null，实际实现时需要从数据库获取
  return null;
}

async function getRelatedPosts(
  postId: string,
  categorySlug: string,
  locale: Locale,
  limit: number = 3
): Promise<BlogPost[]> {
  // 这里应该是实际的数据库查询
  return [];
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const t = await getTranslations({ locale: params.locale, namespace: 'blog' });
  const locale = params.locale as Locale;
  
  const post = await getPostBySlug(params.category, params.slug, locale);
  
  if (!post) {
    notFound();
  }
  
  const relatedPosts = await getRelatedPosts(post.id, params.category, locale);
  const tableOfContents = generateTableOfContents(post.content);
  
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  return (
    <div className="min-h-screen bg-white dark:bg-dark-900">
      {/* 阅读进度指示器 */}
      <ReadingProgress target="article" variant="bar" />
      
      {/* 文章头部 */}
      <header className="bg-gradient-to-br from-mystical-50 to-gold-50 dark:from-dark-800 dark:to-dark-700 py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            {/* 面包屑导航 */}
            <nav className="flex justify-center items-center space-x-2 text-sm text-mystical-600 dark:text-mystical-400 mb-6">
              <Link href="/blog" className="hover:text-mystical-700 dark:hover:text-mystical-300">
                {t('blog')}
              </Link>
              <span>/</span>
              <Link 
                href={`/blog/${post.category.slug}`}
                className="hover:text-mystical-700 dark:hover:text-mystical-300"
              >
                {post.category.name}
              </Link>
              <span>/</span>
              <span className="text-mystical-800 dark:text-mystical-200 font-medium">
                {post.title}
              </span>
            </nav>
            
            {/* 分类标签 */}
            <div className="mb-4">
              <Link
                href={`/blog/${post.category.slug}`}
                className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-mystical-100 dark:bg-dark-700 text-mystical-700 dark:text-mystical-300 hover:bg-mystical-200 dark:hover:bg-dark-600 transition-colors uppercase tracking-wide"
              >
                {post.category.name}
              </Link>
            </div>
            
            {/* 文章标题 */}
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold font-serif text-mystical-900 dark:text-white mb-6 leading-tight">
              {post.title}
            </h1>
            
            {/* 文章摘要 */}
            <p className="text-lg md:text-xl text-mystical-600 dark:text-mystical-300 max-w-3xl mx-auto mb-8 leading-relaxed">
              {post.excerpt}
            </p>
            
            {/* 作者信息和元数据 */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-sm text-mystical-500 dark:text-mystical-400">
              {/* 作者信息 */}
              <div className="flex items-center gap-3">
                {post.author.avatar && (
                  <Image
                    src={post.author.avatar}
                    alt={post.author.name}
                    width={48}
                    height={48}
                    className="rounded-full border-2 border-mystical-200 dark:border-dark-600"
                  />
                )}
                <div className="text-left">
                  <p className="font-medium text-mystical-900 dark:text-white">
                    {post.author.name}
                  </p>
                  <p className="text-xs">
                    {formatDate(post.publishedAt || post.createdAt)}
                  </p>
                </div>
              </div>
              
              {/* 文章统计 */}
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>{post.readingTime} {t('readTime')}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Eye className="w-4 h-4" />
                  <span>{post.viewCount}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Heart className="w-4 h-4" />
                  <span>{post.likeCount}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 封面图片 */}
      {post.coverImage && (
        <div className="container mx-auto px-4 -mt-8 mb-12">
          <div className="max-w-4xl mx-auto">
            <Image
              src={post.coverImage}
              alt={post.title}
              width={1200}
              height={675}
              className="w-full aspect-[16/9] object-cover rounded-2xl shadow-mystical-lg"
              priority
            />
          </div>
        </div>
      )}

      {/* 主要内容区域 */}
      <div className="container mx-auto px-4 pb-16">
        <div className="flex gap-8">
          {/* 浮动目录 - 仅在桌面端显示 */}
          {tableOfContents.length > 0 && (
            <TableOfContents
              items={tableOfContents}
              variant="floating"
              className="hidden xl:block"
            />
          )}
          
          {/* 文章内容 */}
          <div className="flex-1 max-w-none">
            {/* 移动端目录 */}
            <MobileTableOfContents items={tableOfContents} className="mb-8" />
            
            {/* 文章正文 */}
            <article 
              className="max-w-[680px] mx-auto prose prose-lg prose-mystical dark:prose-invert"
              style={{ 
                fontSize: '1.25rem',
                lineHeight: '1.75',
              }}
            >
              {/* 这里应该渲染Markdown内容 */}
              <div dangerouslySetInnerHTML={{ __html: post.content }} />
            </article>
            
            {/* 文章底部 */}
            <div className="max-w-[680px] mx-auto mt-16">
              <ArticleFooter post={post} relatedPosts={relatedPosts} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// 文章底部组件
function ArticleFooter({ 
  post, 
  relatedPosts 
}: { 
  post: BlogPost; 
  relatedPosts: BlogPost[] 
}) {
  return (
    <div className="space-y-12">
      {/* 标签 */}
      {post.tags.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold text-mystical-900 dark:text-white mb-4">
            相关标签
          </h3>
          <div className="flex flex-wrap gap-2">
            {post.tags.map((tag) => (
              <Link
                key={tag.id}
                href={`/blog/tag/${tag.slug}`}
                className="inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm bg-mystical-100 dark:bg-dark-700 text-mystical-700 dark:text-mystical-300 hover:bg-mystical-200 dark:hover:bg-dark-600 transition-colors"
              >
                <Tag className="w-3 h-3" />
                {tag.name}
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* 社交分享 */}
      <div>
        <h3 className="text-lg font-semibold text-mystical-900 dark:text-white mb-4">
          分享文章
        </h3>
        <div className="flex gap-3">
          <button className="flex items-center justify-center w-10 h-10 rounded-full bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400 hover:bg-mystical-200 dark:hover:bg-dark-600 transition-colors">
            <Share2 className="w-4 h-4" />
          </button>
          <button className="flex items-center justify-center w-10 h-10 rounded-full bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400 hover:bg-mystical-200 dark:hover:bg-dark-600 transition-colors">
            <Heart className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* 作者简介 */}
      <div className="bg-mystical-50 dark:bg-dark-800 p-6 rounded-xl border border-mystical-200 dark:border-dark-700">
        <div className="flex items-start gap-4">
          {post.author.avatar && (
            <Image
              src={post.author.avatar}
              alt={post.author.name}
              width={80}
              height={80}
              className="rounded-full border-2 border-mystical-200 dark:border-dark-600"
            />
          )}
          <div className="flex-1">
            <h4 className="text-lg font-semibold text-mystical-900 dark:text-white mb-2">
              {post.author.name}
            </h4>
            {post.author.bio && (
              <p className="text-mystical-600 dark:text-mystical-300 mb-3">
                {post.author.bio}
              </p>
            )}
            {post.author.socialLinks && (
              <div className="flex gap-2">
                {post.author.socialLinks.twitter && (
                  <a
                    href={post.author.socialLinks.twitter}
                    className="text-mystical-500 hover:text-mystical-600 transition-colors"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Twitter
                  </a>
                )}
                {post.author.socialLinks.website && (
                  <a
                    href={post.author.socialLinks.website}
                    className="text-mystical-500 hover:text-mystical-600 transition-colors"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Website
                  </a>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 相关文章 */}
      {relatedPosts.length > 0 && (
        <div>
          <h3 className="text-2xl font-bold text-mystical-900 dark:text-white mb-6">
            相关文章
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {relatedPosts.map((relatedPost) => (
              <Link
                key={relatedPost.id}
                href={`/blog/${relatedPost.category.slug}/${relatedPost.slug}`}
                className="group"
              >
                <div className="bg-white dark:bg-dark-800 rounded-xl border border-mystical-200 dark:border-dark-700 overflow-hidden hover:shadow-mystical-lg transition-all duration-300">
                  {relatedPost.coverImage && (
                    <Image
                      src={relatedPost.coverImage}
                      alt={relatedPost.title}
                      width={400}
                      height={225}
                      className="w-full aspect-[16/9] object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  )}
                  <div className="p-4">
                    <h4 className="font-semibold text-mystical-900 dark:text-white mb-2 line-clamp-2 group-hover:text-mystical-700 dark:group-hover:text-mystical-300 transition-colors">
                      {relatedPost.title}
                    </h4>
                    <p className="text-sm text-mystical-600 dark:text-mystical-400 line-clamp-2">
                      {relatedPost.excerpt}
                    </p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
