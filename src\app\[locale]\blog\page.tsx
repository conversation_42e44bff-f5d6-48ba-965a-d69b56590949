import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import { Suspense } from 'react';
import { BlogList, BlogListSkeleton } from '@/components/blog';
import { BlogPost, BlogCategory, Locale } from '@/types';

interface BlogPageProps {
  params: {
    locale: string;
  };
  searchParams: {
    page?: string;
    category?: string;
    tag?: string;
    search?: string;
  };
}

export async function generateMetadata({ params }: BlogPageProps): Promise<Metadata> {
  const t = await getTranslations({ locale: params.locale, namespace: 'blog' });
  
  return {
    title: t('title'),
    description: t('description'),
    keywords: [
      t('keywords.mystical'),
      t('keywords.tarot'),
      t('keywords.astrology'),
      t('keywords.numerology'),
      t('keywords.crystal'),
      t('keywords.palmistry'),
      t('keywords.dreams'),
    ],
    openGraph: {
      title: t('title'),
      description: t('description'),
      type: 'website',
      locale: params.locale,
    },
  };
}

// 模拟数据获取函数 - 实际项目中应该从数据库获取
async function getBlogPosts(
  locale: Locale,
  page: number = 1,
  limit: number = 12,
  filters?: {
    category?: string;
    tag?: string;
    search?: string;
  }
): Promise<{
  posts: BlogPost[];
  featuredPost?: BlogPost;
  totalPages: number;
  categories: BlogCategory[];
}> {
  // 这里应该是实际的数据库查询
  // 暂时返回模拟数据
  const mockPosts: BlogPost[] = [];
  const mockCategories: BlogCategory[] = [];
  
  return {
    posts: mockPosts,
    featuredPost: undefined,
    totalPages: 1,
    categories: mockCategories,
  };
}

export default async function BlogPage({ params, searchParams }: BlogPageProps) {
  const t = await getTranslations({ locale: params.locale, namespace: 'blog' });
  const locale = params.locale as Locale;
  
  const page = parseInt(searchParams.page || '1', 10);
  const { posts, featuredPost, totalPages, categories } = await getBlogPosts(
    locale,
    page,
    12,
    {
      category: searchParams.category,
      tag: searchParams.tag,
      search: searchParams.search,
    }
  );

  return (
    <div className="min-h-screen bg-white dark:bg-dark-900">
      {/* 页面头部 */}
      <div className="bg-gradient-to-br from-mystical-50 to-gold-50 dark:from-dark-800 dark:to-dark-700 py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold font-serif text-mystical-900 dark:text-white mb-4">
            {t('title')}
          </h1>
          <p className="text-lg md:text-xl text-mystical-600 dark:text-mystical-300 max-w-2xl mx-auto mb-8">
            {t('description')}
          </p>
          
          {/* 分类导航 */}
          <div className="flex flex-wrap justify-center gap-3">
            <a
              href="/blog"
              className="px-4 py-2 bg-white dark:bg-dark-800 text-mystical-600 dark:text-mystical-400 rounded-full border border-mystical-200 dark:border-dark-600 hover:bg-mystical-50 dark:hover:bg-dark-700 transition-colors text-sm font-medium"
            >
              {t('allCategories')}
            </a>
            {categories.map((category) => (
              <a
                key={category.id}
                href={`/blog/${category.slug}`}
                className="px-4 py-2 bg-white dark:bg-dark-800 text-mystical-600 dark:text-mystical-400 rounded-full border border-mystical-200 dark:border-dark-600 hover:bg-mystical-50 dark:hover:bg-dark-700 transition-colors text-sm font-medium"
              >
                {category.name}
              </a>
            ))}
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 主内容区 */}
          <div className="lg:col-span-3">
            <Suspense fallback={<BlogListSkeleton showFeatured={!!featuredPost} columns={2} />}>
              <BlogList
                posts={posts}
                featuredPost={featuredPost}
                showFeatured={page === 1} // 只在第一页显示特色文章
                layout="grid"
                columns={2}
              />
            </Suspense>
          </div>

          {/* 侧边栏 */}
          <div className="lg:col-span-1">
            <BlogSidebar categories={categories} />
          </div>
        </div>
      </div>
    </div>
  );
}

// 博客侧边栏组件
function BlogSidebar({ categories }: { categories: BlogCategory[] }) {
  return (
    <div className="space-y-8">
      {/* 搜索框 */}
      <div className="bg-white dark:bg-dark-800 p-6 rounded-xl border border-mystical-200 dark:border-dark-700">
        <h3 className="text-lg font-semibold text-mystical-900 dark:text-white mb-4">
          搜索文章
        </h3>
        <div className="relative">
          <input
            type="text"
            placeholder="输入关键词..."
            className="w-full px-4 py-2 pl-10 text-sm border border-mystical-200 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-700 text-mystical-800 dark:text-mystical-200 placeholder-mystical-400 dark:placeholder-mystical-500 focus:outline-none focus:ring-2 focus:ring-mystical-500 focus:border-transparent"
          />
          <svg
            className="absolute left-3 top-2.5 w-4 h-4 text-mystical-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>

      {/* 热门分类 */}
      <div className="bg-white dark:bg-dark-800 p-6 rounded-xl border border-mystical-200 dark:border-dark-700">
        <h3 className="text-lg font-semibold text-mystical-900 dark:text-white mb-4">
          热门分类
        </h3>
        <div className="space-y-2">
          {categories.slice(0, 6).map((category) => (
            <a
              key={category.id}
              href={`/blog/${category.slug}`}
              className="flex items-center justify-between p-2 rounded-lg hover:bg-mystical-50 dark:hover:bg-dark-700 transition-colors"
            >
              <span className="text-sm text-mystical-700 dark:text-mystical-300">
                {category.name}
              </span>
              <span className="text-xs text-mystical-500 dark:text-mystical-400 bg-mystical-100 dark:bg-dark-600 px-2 py-1 rounded-full">
                {category.postCount}
              </span>
            </a>
          ))}
        </div>
      </div>

      {/* 订阅表单 */}
      <div className="bg-gradient-to-br from-mystical-50 to-gold-50 dark:from-dark-800 dark:to-dark-700 p-6 rounded-xl border border-mystical-200 dark:border-dark-600">
        <h3 className="text-lg font-semibold text-mystical-900 dark:text-white mb-2">
          订阅更新
        </h3>
        <p className="text-sm text-mystical-600 dark:text-mystical-300 mb-4">
          获取最新的玄学知识和测试内容
        </p>
        <form className="space-y-3">
          <input
            type="email"
            placeholder="输入邮箱地址"
            className="w-full px-3 py-2 text-sm border border-mystical-200 dark:border-dark-600 rounded-lg bg-white dark:bg-dark-700 text-mystical-800 dark:text-mystical-200 placeholder-mystical-400 dark:placeholder-mystical-500 focus:outline-none focus:ring-2 focus:ring-mystical-500 focus:border-transparent"
          />
          <button
            type="submit"
            className="w-full px-4 py-2 text-sm font-medium text-white bg-mystical-500 rounded-lg hover:bg-mystical-600 transition-colors"
          >
            订阅
          </button>
        </form>
      </div>
    </div>
  );
}
